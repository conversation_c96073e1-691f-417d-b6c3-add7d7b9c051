import 'package:easy_debounce/easy_throttle.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

/// WebSocket data source manager
/// 管理多个WebSocketDataSource实例，每个URL对应一个实例
/// 设计为单例使用，配合单连接的WebSocketRepository
/// 不再使用引用计数，简化连接管理
class WebSocketDataSourceManager {
  final Map<String, WebSocketDataSource> _dataSources = {};
  final Map<String, Future<ResultWithData<WebSocketDataSource>>>
      _connectingTasks = {};
  final StorageService _storageService;

  WebSocketDataSourceManager({
    required StorageService storageService,
  }) : _storageService = storageService;

  /// 获取指定URL的数据源，如果不存在则创建
  WebSocketDataSource getDataSource(String url) {
    if (!_dataSources.containsKey(url)) {
      _dataSources[url] =
          WebSocketDataSourceImpl(storageService: _storageService, url: url);
      LogUtils.d('Created new WebSocketDataSource for URL: $url',
          tag: 'WebSocketDataSourceManager.getDataSource');
    }

    return _dataSources[url]!;
  }

  /// 移除指定URL的数据源
  Future<void> removeDataSource(String url) async {
    if (!_dataSources.containsKey(url)) {
      return;
    }

    final dataSource = _dataSources[url]!;
    dataSource.dispose();
    _dataSources.remove(url);
    LogUtils.d('Removed WebSocketDataSource for URL: $url',
        tag: 'WebSocketDataSourceManager.removeDataSource');
  }

  /// 断开并移除所有数据源
  Future<void> dispose() async {
    for (final url in _dataSources.keys.toList()) {
      await removeDataSource(url);
    }
    _dataSources.clear();
    _connectingTasks.clear(); // 清理连接任务
    LogUtils.d('Disposed all WebSocketDataSources',
        tag: 'WebSocketDataSourceManager.dispose');
  }

  /// 检查指定URL的连接状态
  bool isConnected(String url) {
    return _dataSources[url]?.isConnected ?? false;
  }

  /// 获取所有活跃连接的URL
  List<String> get activeConnections {
    return _dataSources.entries
        .where((entry) => entry.value.isConnected)
        .map((entry) => entry.key)
        .toList();
  }

  /// 连接到指定URL
  Future<ResultWithData<WebSocketDataSource>> connect(String url) async {
    // 如果已有连接任务在进行，等待其完成（带超时保护）
    if (_connectingTasks.containsKey(url)) {
      LogUtils.d('URL $url 的连接任务已在进行中，等待完成',
          tag: 'WebSocketDataSourceManager.connect');
      try {
        return await _connectingTasks[url]!.timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            LogUtils.w('连接任务超时，移除任务锁 - $url',
                tag: 'WebSocketDataSourceManager.connect');
            _connectingTasks.remove(url);
            return Either.left(const AppException(
              statusCode: 408,
              message: 'Connection task timeout',
              identifier: 'WS_CONNECT_TIMEOUT',
            ));
          },
        );
      } catch (e) {
        _connectingTasks.remove(url);
        rethrow;
      }
    }

    // 获取数据源并检查连接状态
    final dataSource = getDataSource(url);
    if (dataSource.isConnected) {
      LogUtils.d('URL $url 已连接，无需重复连接',
          tag: 'WebSocketDataSourceManager.connect');
      return Right(dataSource);
    }

    // 创建连接任务
    final connectTask = _performConnect(dataSource);
    _connectingTasks[url] = connectTask;

    try {
      return await connectTask;
    } catch (e) {
      LogUtils.e('连接任务异常 - $url: $e',
          tag: 'WebSocketDataSourceManager.connect');
      rethrow;
    } finally {
      // 确保任务完成后清理
      _connectingTasks.remove(url);
    }
  }

  Future<ResultWithData<WebSocketDataSource>> _performConnect(
      WebSocketDataSource dataSource) async {
    final result = await dataSource.connect();

    return result.fold(
      (error) => Either.left(error),
      (_) => Right(dataSource),
    );
  }

  /// 断开指定URL的连接
  Future<ResultWithData<void>> disconnect(String url) async {
    if (!_dataSources.containsKey(url)) {
      return Either.left(AppException(
        statusCode: 404,
        message: 'No connection found for URL: $url',
        identifier: 'WS_CONNECTION_NOT_FOUND',
      ));
    }

    final result =
        await _dataSources[url]!.disconnect(closeCode: CloseCode.goingAway);
    await removeDataSource(url);
    return result;
  }

  /// 获取当前活跃的数据源数量
  int get activeDataSourceCount => _dataSources.length;

  /// 重置重连状态并检查连接（原子操作）
  /// 这个方法确保重置和检查连接是原子性的，避免竞态条件
  Future<void> resetAndCheckAllConnections() async {
    EasyThrottle.throttle(
      'resetAndCheckAllConnections',
      const Duration(seconds: 2),
      () async {
        LogUtils.d("Resetting and checking all WebSocket connections",
            tag: "WebSocketDataSourceManager.resetAndCheckAllConnections");

        // 先重置所有重连状态
        for (final dataSource in _dataSources.values) {
          dataSource.resetReconnectionState();
        }

        // 等待一小段时间确保重置完成
        await Future.delayed(const Duration(milliseconds: 100));

        // 然后检查并重连
        if (_dataSources.isEmpty) {
          return;
        }

        for (final url in _dataSources.keys.toList()) {
          final dataSource = _dataSources[url];
          if (dataSource == null) continue;

          final isConnected = dataSource.isConnected;
          LogUtils.d(
              "Connection to $url is ${isConnected ? "active" : "inactive"}",
              tag: "WebSocketDataSourceManager.resetAndCheckAllConnections");

          if (!isConnected) {
            final result = await connect(url);
            result.fold(
              (error) {
                LogUtils.e("Failed to reconnect to $url: ${error.message}",
                    tag:
                        "WebSocketDataSourceManager.resetAndCheckAllConnections");
              },
              (_) {
                LogUtils.d("Successfully reconnected to $url",
                    tag:
                        "WebSocketDataSourceManager.resetAndCheckAllConnections");
              },
            );
          }
        }
      },
    );
  }
}
